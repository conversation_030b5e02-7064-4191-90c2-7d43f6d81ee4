<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\City;
use App\Models\cartMod;
use App\Models\Country;
use App\Models\OrderTab;
use App\Models\ChargeTab;
use App\Mail\InvoiceMail;
use App\Models\Inventory;
use App\Models\BillingTab;
use App\Models\Product_list;
use App\Models\Color;
use App\Models\Size;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\OrdereditemsTab;
use App\Http\Controllers\Controller;
use App\Mail\OrderPlaced;
use App\Models\User;
use App\Events\NewOrderPlaced;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use App\Notifications\NewOrderNotification;
use App\Http\Requests\GuestCheckoutRequest;
use App\Services\CartService;
use App\Services\GuestRegistrationService;
use Illuminate\Support\Facades\DB;

class CheckoutCont extends Controller
{
    // Checkout Page ===
    public function checkout(){
        $cart_info = collect();
        $is_guest = false;

        if(Auth::guard('cust_login')->check()){
            // Registered user - get cart from database
            $cart_info = cartMod::where('customer_id', Auth::guard('cust_login')->id())->get();
        } else {
            // Guest user - get cart from session
            $is_guest = true;
            $guest_cart = session()->get('guest_cart', []);

            // Check if guest cart is empty
            if (empty($guest_cart)) {
                return redirect()->route('shop_page')->with('error', 'Your cart is empty');
            }

            // Convert session cart to collection with product relationships
            foreach ($guest_cart as $key => $item) {
                $product = Product_list::find($item['product_id']);
                $color = Color::find($item['color_id']);
                $size = Size::find($item['size_id']);

                if ($product && $color && $size) {
                    $cart_item = (object) [
                        'id' => $key,
                        'product_id' => $item['product_id'],
                        'color_id' => $item['color_id'],
                        'size_id' => $item['size_id'],
                        'quantity' => $item['quantity'],
                        'item_type' => $item['item_type'],
                        'customer_picture' => $item['customer_picture'],
                        'created_at' => $item['created_at'],
                        'relto_product' => $product,
                        'relto_color' => $color,
                        'relto_size' => $size,
                    ];
                    $cart_info->push($cart_item);
                }
            }
        }

        $countries = Country::all();
        $delivery_locations = ChargeTab::all();

        return view('frontend.checkout', [
            'cart_info' => $cart_info,
            'countries' => $countries,
            'delivery_locations' => $delivery_locations,
            'is_guest' => $is_guest,
        ]);
    }

    // Ajax: Get City ===
    public function get_city(Request $request){
        $sel_city = City::where('country_id', $request->country_id)->get();

        $str = '<option value="">-- Select City --</option>';
        foreach ($sel_city as $city){
            $str .= "<option value='$city->id'>$city->name</option>";
        }
        echo $str;
    }

    // Ajax: Get Code ===
    public function get_code(Request $request){
        $sel_code = Country::find($request->country_id)->phonecode;
        $str = "<option value='$sel_code'>$sel_code</option>";
        echo $str;
    }

    // Insert: Billing ===
    public function billing_details(GuestCheckoutRequest $request)
    {
        try {
            // Validated data is automatically sanitized by the Form Request
            $validatedData = $request->validated();

            $name = $validatedData['name'];
            $gtotal = $validatedData['ftotal'] + $validatedData['delivery_charge'];
            $mobile = $validatedData['code'] . $validatedData['mobile'];

            $city = City::find($validatedData['city'])->name;
            $city_sp = Str::upper(substr($city, 0, 3));
            $order_id = '#' . $city_sp . '-' . random_int(100000, 999999);

            // Store order information in session
            session([
                'order_info' => $validatedData,
                'name' => $name,
                'gtotal' => $gtotal,
                'mobile' => $mobile,
                'order_id' => $order_id,
            ]);

            // Handle guest registration if requested
            if ($request->has('create_account') && $request->create_account) {
                session(['create_account_after_order' => true]);
            }

            // Payment Gateway Routing
            switch ($validatedData['payment_method']) {
                case 2:
                    return redirect()->route('ssl.pay');
                case 3:
                    return redirect()->route('stripe');
                default:
                    return redirect()->route('order.complete')->with([
                        'order_conf' => $order_id,
                    ]);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'An error occurred while processing your order. Please try again.'])
                        ->withInput();
        }
    }

    // === Order Complete ===
    public function order_complete(Request $request){
        if(!session('order_info')){
            return redirect()->route('home_page');
        }
        else {
            $order_info = session('order_info');
            $name = session('name');
            $gtotal = session('gtotal');
            $mobile = session('mobile');
            $order_id = session('order_id');
            
            // Get cart info for both guest and registered users
            $cart_info = collect();
            $customer_id = null;

            if(Auth::guard('cust_login')->check()){
                // Registered user
                $cart_info = cartMod::where('customer_id', Auth::guard('cust_login')->id())->get();
                $customer_id = Auth::guard('cust_login')->id();
            } else {
                // Guest user - get cart from session
                $guest_cart = session()->get('guest_cart', []);

                // Convert session cart to collection with product relationships
                foreach ($guest_cart as $key => $item) {
                    $product = Product_list::find($item['product_id']);
                    $color = Color::find($item['color_id']);
                    $size = Size::find($item['size_id']);

                    if ($product && $color && $size) {
                        $cart_item = (object) [
                            'id' => $key,
                            'product_id' => $item['product_id'],
                            'color_id' => $item['color_id'],
                            'size_id' => $item['size_id'],
                            'quantity' => $item['quantity'],
                            'item_type' => $item['item_type'],
                            'customer_picture' => $item['customer_picture'],
                            'created_at' => $item['created_at'],
                            'relto_product' => $product,
                            'relto_color' => $color,
                            'relto_size' => $size,
                        ];
                        $cart_info->push($cart_item);
                    }
                }
            }

            // === Order Table ===
            $order = OrderTab::create([
                'order_id' => $order_id,
                'customer_id' => $customer_id, // null for guest users
                'total' => $order_info['subtotal'],
                'discount' => $order_info['discount'],
                'charge' => $order_info['delivery_charge'],
                'gtotal' => $gtotal,
                'payment_method' => $order_info['payment_method'],
                'order_status' => 1,
                'created_at' => Carbon::now(),
            ]);

            // Fire event for new order
            event(new NewOrderPlaced($order));

            // === Billing Table ===
            BillingTab::insert([
                'order_id' => $order_id,
                'customer_id' => $customer_id, // Use null for guests, user ID for authenticated users
                'name' => $order_info['name'],
                'email' => $order_info['email'],
                'company' => $order_info['company_name'] ?? null,
                'mobile' => $mobile,
                'address' => $order_info['delivery_address'],
                'country' => $order_info['country'],
                'city' => $order_info['city'],
                'zip' => $order_info['postal_code'],
                'note' => $order_info['additional_information'] ?? null,
                'created_at' => Carbon::now(),
            ]);

            // === Ordered Items Table ===
            foreach($cart_info as $cart){
                OrdereditemsTab::insert([
                    'order_id' => $order_id,
                    'customer_id' => $customer_id, // null for guest users
                    'product_id' => $cart->product_id,
                    'color_id' => $cart->color_id,
                    'size_id' => $cart->size_id,
                    'quantity' => $cart->quantity,
                    'price' => $cart->relto_product->after_disc,
                    'item_type' => $cart->item_type,
                    'customer_picture' => $cart->customer_picture,
                    'created_at' => Carbon::now(),
                ]);

                // === Subtract Inventory ===
                // Inventory::where('product_id', $cart->product_id)->where('color', $cart->color_id)->where('size', $cart->size_id)->decrement('quantity', $cart->quantity);
            }

            // === Delete Cart ===
            if(Auth::guard('cust_login')->check()){
                // Delete database cart for registered users
                // cartMod::where('customer_id', Auth::guard('cust_login')->id())->delete();
            } else {
                // Clear session cart for guest users
                session()->forget('guest_cart');
            }

            // === Order Invoice View (/mailable) ===
            Session([
                'mail_item' => $order_id,
            ]);
            
            // === Send Mail ===
            if(Auth::guard('cust_login')->check()){
                // Registered user email logic
                if($order_info['email'] == Auth::guard('cust_login')->user()->email){
                    Mail::to($order_info['email'])->send(new OrderPlaced($order_id));
                }
                else {
                    Mail::to($order_info['email'])
                    ->cc(Auth::guard('cust_login')->user()->email)
                    ->send(new OrderPlaced($order_id));
                }
            } else {
                // Guest user - send email to provided address only
                Mail::to($order_info['email'])->send(new OrderPlaced($order_id));
            }
            


            // === SMS API ===
            // $url = "http://bulksmsbd.net/api/smsapi";
            // $api_key = "yKJN2zqa39nfLpEMRFV2";
            // $senderid = "8809612443872";
            // $number = "$mobile";
            // $message = "Dear ".$name.", Your Order ".$order_id." is Placed. Total Bill: ".$gtotal." Tk. Thanks for Shopping with Kumo E-store.";
            
            // $data = [
            //     "api_key" => $api_key,
            //     "senderid" => $senderid,
            //     "number" => $number,
            //     "message" => $message
            // ];
            // $ch = curl_init();
            // curl_setopt($ch, CURLOPT_URL, $url);
            // curl_setopt($ch, CURLOPT_POST, 1);
            // curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            // $response = curl_exec($ch);
            // curl_close($ch);
            // return $response;

            // === Handle Guest Registration ===
            if (session('create_account_after_order') && !Auth::guard('cust_login')->check()) {
                try {
                    $guestRegistrationService = new GuestRegistrationService();
                    $customer = $guestRegistrationService->createAccountFromGuestOrder($order_info, $order_id);

                    // Auto-login the newly created user
                    Auth::guard('cust_login')->login($customer);

                    session()->flash('account_created', 'Your account has been created successfully! You are now logged in.');
                } catch (\Exception $e) {
                    // Log error but don't fail the order
                    \Log::error('Guest registration failed: ' . $e->getMessage());
                    session()->flash('account_creation_failed', 'Order completed successfully, but account creation failed. You can create an account later.');
                }
                session()->forget('create_account_after_order');
            }

            // === Delete Order Session ===
            session::pull('order_info');
            session::pull('name');
            session::pull('gtotal');
            session::pull('mobile');
            session::pull('order_id');

            // === Order Invoice Session Pull ===
            // session::pull('mail_item');

            // Send notification to all admin users
            $adminUsers = User::role('admin')->get();
            foreach ($adminUsers as $admin) {
                $admin->notify(new NewOrderNotification($order));
            }

            return view('frontend.order_complete');
        }
    }

    // === Order Failed ===
    public function order_failed(){
        return view('frontend.order_failed');
    }
}
