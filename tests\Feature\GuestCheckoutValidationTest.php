<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Country;
use App\Models\City;
use App\Models\ChargeTab;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class GuestCheckoutValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $country = Country::create([
            'name' => 'Finland',
            'phonecode' => '+358'
        ]);
        
        City::create([
            'name' => 'Helsinki',
            'country_id' => $country->id
        ]);
        
        ChargeTab::create([
            'location' => 'Helsinki',
            'charge' => 5.00
        ]);
        
        // Add items to guest cart
        session(['guest_cart' => [
            [
                'product_id' => 1,
                'color_id' => 1,
                'size_id' => 1,
                'quantity' => 1,
                'item_type' => null,
                'customer_picture' => null
            ]
        ]]);
        
        session(['total' => 100, 'discount' => 0, 'ftotal' => 100]);
    }

    /** @test */
    public function guest_checkout_requires_all_mandatory_fields()
    {
        $response = $this->post(route('billing.store'), []);

        $response->assertSessionHasErrors([
            'name',
            'email',
            'mobile',
            'code',
            'delivery_address',
            'country',
            'city',
            'postal_code',
            'delivery_charge',
            'payment_method'
        ]);
    }

    /** @test */
    public function guest_checkout_validates_field_formats()
    {
        $response = $this->post(route('billing.store'), [
            'name' => 'A', // Too short
            'email' => 'invalid-email', // Invalid format
            'mobile' => '123', // Too short
            'code' => 'invalid', // Invalid format
            'delivery_address' => 'A', // Too short
            'postal_code' => 'AB', // Too short
            'company_name' => str_repeat('A', 300), // Too long
            'additional_information' => str_repeat('A', 1100), // Too long
            'country' => 999, // Non-existent
            'city' => 999, // Non-existent
            'delivery_charge' => 5,
            'payment_method' => 1,
            'subtotal' => 100,
            'discount' => 0,
            'ftotal' => 100
        ]);

        $response->assertSessionHasErrors([
            'name',
            'email',
            'mobile',
            'code',
            'delivery_address',
            'postal_code',
            'company_name',
            'additional_information',
            'country',
            'city'
        ]);
    }

    /** @test */
    public function guest_checkout_accepts_valid_data()
    {
        $country = Country::first();
        $city = City::first();

        $validData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '1234567890',
            'code' => '+358',
            'delivery_address' => '123 Main Street, Apartment 4B',
            'country' => $country->id,
            'city' => $city->id,
            'postal_code' => '00100',
            'company_name' => 'Test Company',
            'additional_information' => 'Please ring the doorbell',
            'delivery_charge' => 5,
            'payment_method' => 1,
            'subtotal' => 100,
            'discount' => 0,
            'ftotal' => 100
        ];

        $response = $this->post(route('billing.store'), $validData);

        $response->assertRedirect(route('order.complete'));
        $this->assertNotNull(session('order_info'));
    }

    /** @test */
    public function guest_checkout_validates_email_contains_at_symbol()
    {
        $response = $this->post(route('billing.store'), [
            'email' => 'invalidemail.com', // Missing @ symbol
            'name' => 'John Doe',
            'mobile' => '1234567890',
            'code' => '+358',
            'delivery_address' => '123 Main Street',
            'country' => Country::first()->id,
            'city' => City::first()->id,
            'postal_code' => '00100',
            'delivery_charge' => 5,
            'payment_method' => 1,
            'subtotal' => 100,
            'discount' => 0,
            'ftotal' => 100
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function guest_checkout_validates_mobile_number_length()
    {
        $country = Country::first();
        $city = City::first();

        // Test too short mobile number
        $response = $this->post(route('billing.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '123456', // 6 digits - too short
            'code' => '+358',
            'delivery_address' => '123 Main Street',
            'country' => $country->id,
            'city' => $city->id,
            'postal_code' => '00100',
            'delivery_charge' => 5,
            'payment_method' => 1,
            'subtotal' => 100,
            'discount' => 0,
            'ftotal' => 100
        ]);

        $response->assertSessionHasErrors(['mobile']);

        // Test too long mobile number
        $response = $this->post(route('billing.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '1234567890123456', // 16 digits - too long
            'code' => '+358',
            'delivery_address' => '123 Main Street',
            'country' => $country->id,
            'city' => $city->id,
            'postal_code' => '00100',
            'delivery_charge' => 5,
            'payment_method' => 1,
            'subtotal' => 100,
            'discount' => 0,
            'ftotal' => 100
        ]);

        $response->assertSessionHasErrors(['mobile']);
    }

    /** @test */
    public function guest_checkout_validates_payment_method_options()
    {
        $country = Country::first();
        $city = City::first();

        $response = $this->post(route('billing.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '1234567890',
            'code' => '+358',
            'delivery_address' => '123 Main Street',
            'country' => $country->id,
            'city' => $city->id,
            'postal_code' => '00100',
            'delivery_charge' => 5,
            'payment_method' => 4, // Invalid payment method
            'subtotal' => 100,
            'discount' => 0,
            'ftotal' => 100
        ]);

        $response->assertSessionHasErrors(['payment_method']);
    }

    /** @test */
    public function guest_checkout_preserves_input_on_validation_failure()
    {
        $response = $this->post(route('billing.store'), [
            'name' => 'John Doe',
            'email' => 'invalid-email', // This will fail validation
            'delivery_address' => '123 Main Street'
        ]);

        $response->assertSessionHasInput(['name', 'email', 'delivery_address']);
    }
}
