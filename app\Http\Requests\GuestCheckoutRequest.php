<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GuestCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // Personal Information
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Only letters, spaces, hyphens, apostrophes, dots
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            ],
            'company_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9\s\-\&\.\,]+$/', // Company name characters
            ],

            // Contact Information
            'mobile' => [
                'required',
                'string',
                'min:7',
                'max:15',
                'regex:/^[0-9]+$/', // Only digits for mobile number
            ],
            'code' => [
                'required',
                'string',
                'max:10',
                'regex:/^\+?[0-9]+$/', // Country code format
            ],

            // Address Information
            'delivery_address' => [
                'required',
                'string',
                'min:5',
                'max:500',
                'regex:/^[a-zA-Z0-9\s\-\,\.\/\#]+$/', // Address characters
            ],
            'country' => [
                'required',
                'integer',
                'exists:countries,id',
            ],
            'city' => [
                'required',
                'integer',
                'exists:cities,id',
            ],
            'postal_code' => [
                'required',
                'string',
                'min:3',
                'max:10',
                'regex:/^[a-zA-Z0-9\-\s]+$/', // ZIP/Postal code format
            ],
            'additional_information' => [
                'nullable',
                'string',
                'max:1000',
                'regex:/^[a-zA-Z0-9\s\-\,\.\/\#\(\)\&]+$/', // Safe note characters
            ],
            
            // Order Information
            'delivery_charge' => [
                'required',
                'numeric',
                'min:0',
                'max:9999',
            ],
            'payment_method' => [
                'required',
                'integer',
                Rule::in([1, 2, 3]), // 1=COD, 2=SSL, 3=Stripe
            ],
            'subtotal' => [
                'required',
                'numeric',
                'min:0',
            ],
            'discount' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'ftotal' => [
                'required',
                'numeric',
                'min:0',
            ],
            
            // Guest Registration (optional)
            'create_account' => [
                'nullable',
                'boolean',
            ],
            'password' => [
                'required_if:create_account,true',
                'nullable',
                'string',
                'min:8',
                'max:255',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/', // Strong password
            ],
            'password_confirmation' => [
                'required_if:create_account,true',
                'nullable',
                'same:password',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Full name is required.',
            'name.max' => 'Name cannot exceed 255 characters.',
            'name.regex' => 'Name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address with @ symbol.',
            'email.max' => 'Email cannot exceed 255 characters.',
            'email.regex' => 'Email format is invalid.',
            'mobile.required' => 'Mobile number is required.',
            'mobile.min' => 'Mobile number must be at least 7 digits.',
            'mobile.max' => 'Mobile number cannot exceed 15 digits.',
            'mobile.regex' => 'Mobile number can only contain digits.',
            'code.required' => 'Phone code is required.',
            'code.regex' => 'Country code must contain only numbers and optional plus sign.',
            'delivery_address.required' => 'Delivery address is required.',
            'delivery_address.min' => 'Delivery address must be at least 5 characters long.',
            'delivery_address.max' => 'Delivery address cannot exceed 500 characters.',
            'delivery_address.regex' => 'Delivery address contains invalid characters.',
            'country.required' => 'Please select a country.',
            'city.required' => 'Please select a city.',
            'postal_code.required' => 'Postal code is required.',
            'postal_code.min' => 'Postal code must be at least 3 characters.',
            'postal_code.max' => 'Postal code cannot exceed 10 characters.',
            'postal_code.regex' => 'Postal code format is invalid.',
            'company_name.max' => 'Company name cannot exceed 255 characters.',
            'additional_information.max' => 'Additional information cannot exceed 1000 characters.',
            'additional_information.regex' => 'Additional information contains invalid characters.',
            'delivery_charge.required' => 'Please select a delivery location.',
            'payment_method.required' => 'Please select a payment method.',
            'payment_method.in' => 'Please select a valid payment method.',
            'password.required' => 'Password is required when creating an account.',
            'password.min' => 'Password must be at least 8 characters long.',
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
            'password_confirmation.same' => 'Password confirmation does not match.',
            'country.exists' => 'Please select a valid country.',
            'city.exists' => 'Please select a valid city.',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Sanitize inputs
        $this->merge([
            'name' => $this->sanitizeString($this->name),
            'email' => $this->sanitizeEmail($this->email),
            'company_name' => $this->sanitizeString($this->company_name),
            'mobile' => $this->sanitizeString($this->mobile),
            'delivery_address' => $this->sanitizeString($this->delivery_address),
            'postal_code' => $this->sanitizeString($this->postal_code),
            'additional_information' => $this->sanitizeString($this->additional_information),
        ]);
    }

    /**
     * Sanitize string input
     *
     * @param string|null $value
     * @return string|null
     */
    private function sanitizeString($value)
    {
        if (is_null($value)) {
            return null;
        }
        
        return trim(strip_tags($value));
    }

    /**
     * Sanitize email input
     *
     * @param string|null $value
     * @return string|null
     */
    private function sanitizeEmail($value)
    {
        if (is_null($value)) {
            return null;
        }
        
        return trim(strtolower(strip_tags($value)));
    }
}
